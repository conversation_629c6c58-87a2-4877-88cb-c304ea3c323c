# Importamos as bibliotecas necessárias
import MetaTrader5 as mt5
import pandas as pd # A convenção é importar pandas como 'pd'
from datetime import datetime
import mplfinance as mpf
import tkinter as tk

# --- CONEXÃO COM O METATRADER 5 ---
if not mt5.initialize():
    print("A inicialização do MetaTrader 5 falhou. Código de erro =", mt5.last_error())
    quit()

# --- PARÂMETROS PARA OBTER OS DADOS ---
simbolo = "BTCUSD"
timeframe = mt5.TIMEFRAME_M5 # Gráfico de 5 minutos
quantidade_barras = 10      # Quantas das últimas velas queremos obter

print(f"A obter as últimas {quantidade_barras} velas de {simbolo} no timeframe M5...")

# --- A FUNÇÃO PRINCIPAL: copy_rates_from_pos ---
# Documentação:
# mt5.copy_rates_from_pos(simbolo, timeframe, posição_inicial, quantidade)
# - posição_inicial = 0 é a vela atual (que ainda está a ser formada).
# - posição_inicial = 1 é a última vela fechada.
dados_velas = mt5.copy_rates_from_pos(simbolo, timeframe, 1, quantidade_barras)

# --- ENCERRAR A CONEXÃO ---
# É uma boa prática encerrar a conexão assim que obtemos os dados de que precisamos.
mt5.shutdown()
print("Conexão com MT5 encerrada.")

# --- CONVERTER OS DADOS PARA UM FORMATO AMIGÁVEL (PANDAS DATAFRAME) ---
# Documentação: A função do MT5 devolve os dados num formato técnico.
# Convertemos para um DataFrame do Pandas, que é como uma tabela de Excel,
# muito mais fácil de ler e manipular.
df = pd.DataFrame(dados_velas)

# Converter a coluna 'time' de um número para um formato de data e hora legível
df['time'] = pd.to_datetime(df['time'], unit='s')

print("\n--- Tabela de Dados das Velas ---")
print(df)

# --- COMO ACESSAR UM DADO ESPECÍFICO ---
# Documentação: Agora podemos aceder a qualquer dado facilmente.
# Para obter os dados da ÚLTIMA VELA FECHADA (a última linha da nossa tabela)
if not df.empty:
    ultima_vela = df.iloc[-1]
    print("\n--- Detalhes da Última Vela Fechada ---")
    print(f"Data e Hora: {ultima_vela['time']}")
    print(f"Abertura: {ultima_vela['open']}")
    print(f"Máxima: {ultima_vela['high']}")
    print(f"Mínima: {ultima_vela['low']}")
    print(f"Fechamento: {ultima_vela['close']}")
    print(f"Volume: {ultima_vela['tick_volume']}")
else:
    print("Não foram retornados dados.")

if not df.empty:
    df_para_grafico = df.set_index('time')
    
    print("\nA gerar o gráfico numa nova janela...")
    
    # mpf.plot() cria o gráfico.
    # - type='candle': tipo de gráfico de velas.
    # - style='charles': um esquema de cores (pode experimentar outros como 'yahoo').
    # - title, ylabel: Título e rótulos do gráfico.
    # - volume=True: adiciona um painel com o volume.
    mpf.plot(df_para_grafico, 
             type='candle', 
             style='charles', 
             title=f'Gráfico de {simbolo} - M5', 
             ylabel='Preço',
             volume=False)
else:
    print("Não é possível gerar o gráfico porque não há dados.")

    def acao_do_botao():
    # Pega os valores dos campos de texto da janela
    simbolo = campo_simbolo.get()
    barras = int(campo_barras.get())
    
    print(f"Botão clicado! A analisar {simbolo} com {barras} barras...")
    # Aqui você chamaria a sua função que busca os dados e gera o gráfico
    # ex: buscar_e_plotar_dados(simbolo, barras)

