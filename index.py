
# CONSTANTES
bl_bar = close > open
br_bar = close < open 
range_bar = high - low # amplitude da barra
ood = # abertura do dia atual
coy = # fechamento do dia anterior


# tendecia ou lateralidade

if (body > 50%):
    trend_bar
    # exceto em casos onde a trend_bar tem uma amplitude menor que a media, 
    # casos onde eu consigo ver que essa barra de tendencia é uma pausa ela não é tendência

if (pavio > 50%):
    tr_bar
    # exceto em casos onde a tr_bar tem uma amplitude maior que a media e esteja dando continuidade 
    # ou casos onde a tr_bar não tem quase nenhuma sobrepossição com a barra anterior, nesse caso 
    # mesmo se houver um pavio grande no topo, você deve ver essa como uma comprar baixo na lateralidade da barra.          

# tendencia é desequilibrio vertical de preço
bl_bar = close > open
br_bar = close < open

# lateralidade é desequilibrio horizontal de preço
doji_bar = close == open

# comecando a leitura do dia 
# a primeira informação é a abertura do dia
# se é uma abertura positiva então temos uma barra de tendencia portanto se o mercado der continuidade eu não vou querer apostar contra a primeira tentativa de baixa mesmo que a abertura seja lateral.

if (ood > coy): # abertura positiva
    if (firt_bar == bl_bar):
        continuidade = True
        if (first_bar == doji_bar):
            contexto = "lateralidade após rompimento com continuidade"
                # nesse caso tem que tomar cuidado nas vendas, não apostar contra a alta.
                # se tiver um sinal de L1 abaixo de 50% da amplitude diaria você pode apostar contra os vendedores
        else:
            contexto = "tendencia"
    else:
        (first_bar == doji_bar) or (first_bar == br_bar):
        continuidade = False

