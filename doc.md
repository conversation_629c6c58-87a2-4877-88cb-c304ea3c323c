# diretrizes 
- este documento contem as diretrizes de negociação que vão direcionar o algoritmo.

# MATEMATICA 
- se (75%_gain): 
  - calculo_payout()
    - retorno_esperado = (payout * probabilidade_de_acerto) - (perda * probabilidade_de_falha)
      - se retorno 1 -> então você ganha 50% de lucro a cada 100 unidades de risco.    

# barra de sinal
## Barra de tendencia
if (BULL_TREND_BAR or BEAR_TREND_BAR):
    if (TREND):
       if (leg_start):
       comprar close
       elif (leg_correction):
       comprar 75%  
    ELSE:
        if (LATERALIDADE):

    * Barra de Alta ou barra com fechamento acima da média
if (lateralidade):
* Barra de baixa 

## Vender 
if (tendencia):
    * Barra de baixa ou barra com fechamento abaixo da média
if (lateralidade):
* Barra de alta  

# maxima ou minima do dia
calcular quando estatisticamente é confirmada a maxima ou minima do dia.
    o AL fala em esperar até a barra 7 que nela você tem 50 % de chance de ja ter a maxima ou minima do dia

# barras 

## Externa
- a barra externa é essencialmente uma barra que foi acima e abaixo da anterior portanto é uma barra lateral e é uma barra que geralmente falha e o trade bom é em 50% dela
  - se (outside_bear) entao (buy median_price) 