import json
import os
from datetime import datetime

class Codepedia:
    def __init__(self):
        self.arquivo = "codepedia.json"
        self.dados = self.carregar_dados()
    
    def carregar_dados(self):
        """Carrega dados do arquivo JSON ou cria estrutura inicial"""
        if os.path.exists(self.arquivo):
            try:
                with open(self.arquivo, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return self.estrutura_inicial()
        return self.estrutura_inicial()
    
    def estrutura_inicial(self):
        """Estrutura inicial da Codepedia"""
        return {
            "categorias": {
                "python_basico": {},
                "algoritmos": {},
                "web": {},
                "dados": {},
                "projetos": {}
            },
            "tags": {},
            "historico": []
        }
    
    def salvar_dados(self):
        """Salva dados no arquivo JSON"""
        with open(self.arquivo, 'w', encoding='utf-8') as f:
            json.dump(self.dados, f, indent=2, ensure_ascii=False)
    
    def adicionar_codigo(self, categoria, nome, codigo, descricao="", tags=[]):
        """Adiciona um novo código à Codepedia""" 
        if categoria not in self.dados["categorias"]:
            self.dados["categorias"][categoria] = {}
        
        self.dados["categorias"][categoria][nome] = {
            "codigo": codigo,
            "descricao": descricao,
            "tags": tags,
            "data_criacao": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "usos": 0
        }
        
        # Adiciona às tags para busca
        for tag in tags:
            if tag not in self.dados["tags"]:
                self.dados["tags"][tag] = []
            self.dados["tags"][tag].append(f"{categoria}.{nome}")
        
        # Histórico
        self.dados["historico"].append({
            "acao": "adicionado",
            "item": f"{categoria}.{nome}",
            "data": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        
        self.salvar_dados()
        print(f"✅ Código '{nome}' adicionado à categoria '{categoria}'!")
    
    def buscar_codigo(self, termo):
        """Busca códigos por nome, categoria ou tag"""
        resultados = []
        termo = termo.lower()
        
        # Busca nas categorias
        for categoria, codigos in self.dados["categorias"].items():
            if termo in categoria.lower():
                for nome in codigos.keys():
                    resultados.append(f"{categoria}.{nome}")
            
            # Busca nos nomes dos códigos
            for nome, info in codigos.items():
                if (termo in nome.lower() or 
                    termo in info.get("descricao", "").lower() or
                    any(termo in tag.lower() for tag in info.get("tags", []))):
                    resultados.append(f"{categoria}.{nome}")
        
        return list(set(resultados))  # Remove duplicatas
    
    def mostrar_codigo(self, categoria, nome):
        """Mostra um código específico"""
        try:
            codigo_info = self.dados["categorias"][categoria][nome]
            
            # Incrementa contador de uso
            codigo_info["usos"] += 1
            self.salvar_dados()
            
            print(f"\n{'='*50}")
            print(f"📁 Categoria: {categoria}")
            print(f"📝 Nome: {nome}")
            print(f"📖 Descrição: {codigo_info.get('descricao', 'Sem descrição')}")
            print(f"🏷️ Tags: {', '.join(codigo_info.get('tags', []))}")
            print(f"📊 Usos: {codigo_info['usos']}")
            print(f"📅 Criado em: {codigo_info['data_criacao']}")
            print(f"{'='*50}")
            print("💻 CÓDIGO:")
            print(codigo_info["codigo"])
            print(f"{'='*50}")
            
        except KeyError:
            print(f"❌ Código '{categoria}.{nome}' não encontrado!")
    
    def listar_categorias(self):
        """Lista todas as categorias e quantidade de códigos"""
        print("\n📚 CATEGORIAS DA CODEPEDIA:")
        print("-" * 30)
        for categoria, codigos in self.dados["categorias"].items():
            qtd = len(codigos)
            print(f"📁 {categoria}: {qtd} código(s)")
    
    def listar_codigos_categoria(self, categoria):
        """Lista códigos de uma categoria específica"""
        if categoria in self.dados["categorias"]:
            codigos = self.dados["categorias"][categoria]
            print(f"\n📁 Códigos da categoria '{categoria}':")
            print("-" * 40)
            for nome, info in codigos.items():
                usos = info.get('usos', 0)
                tags = ', '.join(info.get('tags', []))
                print(f"📝 {nome} (usado {usos}x) | Tags: {tags}")
        else:
            print(f"❌ Categoria '{categoria}' não encontrada!")
    
    def estatisticas(self):
        """Mostra estatísticas da Codepedia"""
        total_codigos = sum(len(codigos) for codigos in self.dados["categorias"].values())
        total_categorias = len([cat for cat, cod in self.dados["categorias"].items() if cod])
        total_tags = len(self.dados["tags"])
        
        print("\n📊 ESTATÍSTICAS DA CODEPEDIA:")
        print("-" * 35)
        print(f"📝 Total de códigos: {total_codigos}")
        print(f"📁 Categorias ativas: {total_categorias}")
        print(f"🏷️ Total de tags: {total_tags}")
        
        # Códigos mais usados
        if total_codigos > 0:
            todos_codigos = []
            for categoria, codigos in self.dados["categorias"].items():
                for nome, info in codigos.items():
                    todos_codigos.append((f"{categoria}.{nome}", info.get('usos', 0)))
            
            mais_usados = sorted(todos_codigos, key=lambda x: x[1], reverse=True)[:5]
            print(f"\n🔥 TOP 5 MAIS USADOS:")
            for i, (codigo, usos) in enumerate(mais_usados, 1):
                print(f"{i}. {codigo}: {usos} uso(s)")

def menu_principal():
    """Interface principal da Codepedia"""
    codepedia = Codepedia()
    
    while True:
        print(f"\n🚀 CODEPEDIA - Sua Biblioteca de Códigos")
        print("=" * 40)
        print("1. 📝 Adicionar código")
        print("2. 🔍 Buscar código")
        print("3. 👁️ Ver código específico")
        print("4. 📚 Listar categorias")
        print("5. 📁 Ver códigos de uma categoria")
        print("6. 📊 Estatísticas")
        print("7. 🚪 Sair")
        
        escolha = input("\nEscolha uma opção: ").strip()
        
        if escolha == "1":
            print("\n📝 ADICIONAR NOVO CÓDIGO:")
            categoria = input("Categoria: ").strip()
            nome = input("Nome do código: ").strip()
            print("Código (termine com uma linha vazia):")
            linhas_codigo = []
            while True:
                linha = input()
                if linha == "":
                    break
                linhas_codigo.append(linha)
            codigo = "\n".join(linhas_codigo)
            descricao = input("Descrição (opcional): ").strip()
            tags_input = input("Tags separadas por vírgula (opcional): ").strip()
            tags = [tag.strip() for tag in tags_input.split(",") if tag.strip()]
            
            codepedia.adicionar_codigo(categoria, nome, codigo, descricao, tags)
        
        elif escolha == "2":
            termo = input("\n🔍 Digite o termo de busca: ").strip()
            resultados = codepedia.buscar_codigo(termo)
            if resultados:
                print(f"\n✅ Encontrados {len(resultados)} resultado(s):")
                for resultado in resultados:
                    print(f"📝 {resultado}")
            else:
                print("❌ Nenhum código encontrado!")
        
        elif escolha == "3":
            categoria = input("\n👁️ Digite a categoria: ").strip()
            nome = input("Digite o nome do código: ").strip()
            codepedia.mostrar_codigo(categoria, nome)
        
        elif escolha == "4":
            codepedia.listar_categorias()
        
        elif escolha == "5":
            categoria = input("\n📁 Digite o nome da categoria: ").strip()
            codepedia.listar_codigos_categoria(categoria)
        
        elif escolha == "6":
            codepedia.estatisticas()
        
        elif escolha == "7":
            print("👋 Até logo! Sua Codepedia foi salva.")
            break
        
        else:
            print("❌ Opção inválida!")

# Executa a Codepedia
if __name__ == "__main__":
    menu_principal()