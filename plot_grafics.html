<!DOCTYPE html>
<html>
<head>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/plotly.js/2.26.0/plotly.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1e1e1e; color: white; }
        .container { max-width: 1400px; margin: 0 auto; }
        .controls { margin: 20px 0; padding: 15px; background: #2a2a2a; border-radius: 8px; }
        .status-panel { margin: 20px 0; padding: 15px; background: #2a2a2a; border-radius: 8px; }
        .status-item { display: inline-block; margin: 5px 15px; padding: 5px 10px; background: #404040; border-radius: 4px; }
        button { padding: 8px 16px; margin: 5px; background: #007acc; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a9e; }
        #chart { height: 600px; background: #2a2a2a; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Price Action Algorithm - Real Time Visualization</h1>
        
        <div class="controls">
            <button onclick="generateData()">Gerar Dados Simulados</button>
            <button onclick="nextBar()">Próxima Barra</button>
            <button onclick="autoRun()">Auto Run</button>
            <button onclick="reset()">Reset</button>
        </div>

        <div class="status-panel" id="status">
            <div class="status-item">Status: Aguardando...</div>
        </div>

        <div id="chart"></div>
    </div>

    <script>
        // Algoritmo Price Action (versão JavaScript)
        class PriceActionReader {
            constructor() {
                this.bars_processed = 0;
                this.current_leg = null;
                this.leg_start_price = null;
                this.leg_extreme_price = null;
                this.leg_75_percent = null;
                this.day_high = null;
                this.day_low = null;
                this.day_range = null;
                this.day_50_percent = null;
                this.previous_bar = null;
                this.current_bar = null;
                this.market_status = 'neutral';
                this.all_bars = [];
                this.signals = [];
            }

            processBar(bar_data) {
                this.previous_bar = this.current_bar;
                this.current_bar = bar_data;
                this.bars_processed++;
                this.all_bars.push(bar_data);

                if (this.bars_processed === 1) {
                    return this.processFirstBar();
                } else {
                    return this.processSubsequentBar();
                }
            }

            processFirstBar() {
                const bar = this.current_bar;
                
                this.day_high = bar.high;
                this.day_low = bar.low;
                this.day_range = this.day_high - this.day_low;
                this.day_50_percent = this.day_low + (this.day_range * 0.5);

                const body_size = Math.abs(bar.close - bar.open);
                const body_percentage = this.day_range > 0 ? body_size / this.day_range : 0;

                // SEMPRE marca primeira barra como perna (mesmo se lateral)
                this.market_status = 'trend';
                
                if (bar.close > bar.open) {
                    // Perna de alta (ou lateral alta)
                    this.current_leg = 'up';
                    this.leg_start_price = this.day_low;
                    this.leg_extreme_price = this.day_high;
                } else {
                    // Perna de baixa (ou lateral baixa) 
                    this.current_leg = 'down';
                    this.leg_start_price = this.day_high;
                    this.leg_extreme_price = this.day_low;
                }
                
                this.calculate75Percent();
                
                // Marca sinal baseado no tipo da barra
                const signal_type = body_percentage > 0.5 ? 'leg_start' : 'lateral_start';
                this.signals.push({
                    x: this.bars_processed - 1, 
                    type: signal_type, 
                    direction: this.current_leg
                });

                return this.getStatus();
            }

            processSubsequentBar() {
                const bar = this.current_bar;
                
                // Atualiza níveis do dia
                if (bar.high > this.day_high) {
                    this.day_high = bar.high;
                    this.updateDayLevels();
                }
                
                if (bar.low < this.day_low) {
                    this.day_low = bar.low;
                    this.updateDayLevels();
                }

                if (this.current_leg === 'down') {
                    this.processDownLeg();
                } else if (this.current_leg === 'up') {
                    this.processUpLeg();
                } else {
                    this.processNeutralState();
                }

                return this.getStatus();
            }

            processDownLeg() {
                const bar = this.current_bar;
                const prev_bar = this.previous_bar;

                // Verifica se atualizou mínima
                if (bar.low < this.leg_extreme_price) {
                    this.leg_extreme_price = bar.low;
                    this.calculate75Percent();
                    return;
                }

                // Verifica correção de 75%
                if (bar.high >= this.leg_75_percent) {
                    this.current_leg = null;
                    this.market_status = 'lateral';
                    this.signals.push({x: this.bars_processed - 1, type: '75_correction'});
                    return;
                }

                // Verifica fim da perna
                if (bar.low > prev_bar.low && bar.high > prev_bar.high) {
                    this.signals.push({x: this.bars_processed - 1, type: 'leg_end'});
                    this.current_leg = null;
                }
            }

            processUpLeg() {
                const bar = this.current_bar;
                const prev_bar = this.previous_bar;

                // Verifica se atualizou máxima
                if (bar.high > this.leg_extreme_price) {
                    this.leg_extreme_price = bar.high;
                    this.calculate75Percent();
                    return;
                }

                // Verifica correção de 75%
                if (bar.low <= this.leg_75_percent) {
                    this.current_leg = null;
                    this.market_status = 'lateral';
                    this.signals.push({x: this.bars_processed - 1, type: '75_correction'});
                    return;
                }

                // Verifica fim da perna
                if (bar.high < prev_bar.high && bar.low < prev_bar.low) {
                    this.signals.push({x: this.bars_processed - 1, type: 'leg_end'});
                    this.current_leg = null;
                }
            }

            processNeutralState() {
                if (this.bars_processed < 2) return;

                const bar = this.current_bar;
                const prev_bar = this.previous_bar;

                // Verifica se é barra de tendência
                const body_size = Math.abs(bar.close - bar.open);
                const bar_range = bar.high - bar.low;

                if (bar_range === 0) return;

                const body_percentage = body_size / bar_range;
                const is_trend_bar = body_percentage > 0.5;

                if (!is_trend_bar) return;

                const median_price = (bar.high + bar.low) / 2;

                const close_beyond = (bar.close >= prev_bar.high) || (bar.close <= prev_bar.low);
                const median_beyond = (median_price >= prev_bar.high) || (median_price <= prev_bar.low);

                if (close_beyond && median_beyond) {
                    if (bar.close > bar.open) {
                        this.current_leg = 'up';
                        this.leg_start_price = Math.min(bar.low, prev_bar.low);
                        this.leg_extreme_price = bar.high;
                    } else {
                        this.current_leg = 'down';
                        this.leg_start_price = Math.max(bar.high, prev_bar.high);
                        this.leg_extreme_price = bar.low;
                    }

                    this.market_status = 'trend';
                    this.calculate75Percent();
                    this.signals.push({x: this.bars_processed - 1, type: 'new_leg', direction: this.current_leg});
                }
            }

            calculate75Percent() {
                if (this.current_leg === 'down') {
                    const leg_amplitude = this.leg_start_price - this.leg_extreme_price;
                    this.leg_75_percent = this.leg_extreme_price + (leg_amplitude * 0.75);
                } else if (this.current_leg === 'up') {
                    const leg_amplitude = this.leg_extreme_price - this.leg_start_price;
                    this.leg_75_percent = this.leg_extreme_price - (leg_amplitude * 0.75);
                }
            }

            updateDayLevels() {
                this.day_range = this.day_high - this.day_low;
                this.day_50_percent = this.day_low + (this.day_range * 0.5);
            }

            getStatus() {
                return {
                    bar_number: this.bars_processed,
                    current_leg: this.current_leg,
                    market_status: this.market_status,
                    day_high: this.day_high,
                    day_low: this.day_low,
                    day_50: this.day_50_percent,
                    leg_extreme: this.leg_extreme_price,
                    leg_75: this.leg_75_percent
                };
            }
        }

        // Variáveis globais
        let reader = new PriceActionReader();
        let simulatedData = [];
        let currentBarIndex = 0;
        let autoRunning = false;

        // Gera dados simulados (parecidos com mini índice - CORRIGIDO)
        function generateData() {
            simulatedData = [];
            let lastClose = 118000 + Math.random() * 1000; // Preço inicial
            
            for (let i = 0; i < 50; i++) {
                // CORREÇÃO: Gap máximo entre barras = 1-2 ticks (5-10 pontos)
                const maxGap = Math.random() < 0.8 ? 0 : (Math.random() < 0.5 ? 5 : 10); // 80% sem gap, 15% gap de 1 tick, 5% gap de 2 ticks
                const gapDirection = Math.random() < 0.5 ? 1 : -1;
                const open = Math.round((lastClose + (maxGap * gapDirection)) / 5) * 5;
                
                // Range da barra pode ser normal (50-300 pontos dependendo da volatilidade)
                const volatility = 50 + Math.random() * 150; // Volatilidade realista
                const direction = Math.random() - 0.5;
                
                const highMove = Math.abs(direction * volatility) + Math.random() * 50;
                const lowMove = Math.abs(direction * volatility) + Math.random() * 50;
                
                let high = Math.round((open + highMove) / 5) * 5;
                let low = Math.round((open - lowMove) / 5) * 5;
                
                // Garante que high >= open >= low
                if (high < open) high = open + 5;
                if (low > open) low = open - 5;
                
                // Close dentro do range da barra (pode ter qualquer valor dentro do OHLC)
                const closePosition = Math.random(); // 0 = na mínima, 1 = na máxima
                let close = Math.round((low + (closePosition * (high - low))) / 5) * 5;
                
                simulatedData.push({
                    open: open,
                    high: high,
                    low: low,
                    close: close
                });
                
                // IMPORTANTE: Próxima barra abre próxima do close atual (com gap mínimo)
                lastClose = close;
            }
            
            reset();
            updateChart();
        }

        function nextBar() {
            if (currentBarIndex >= simulatedData.length) return;
            
            const status = reader.processBar(simulatedData[currentBarIndex]);
            currentBarIndex++;
            
            updateStatus(status);
            updateChart();
        }

        function autoRun() {
            if (autoRunning) return;
            autoRunning = true;
            
            const interval = setInterval(() => {
                if (currentBarIndex >= simulatedData.length) {
                    clearInterval(interval);
                    autoRunning = false;
                    return;
                }
                nextBar();
            }, 500);
        }

        function reset() {
            reader = new PriceActionReader();
            currentBarIndex = 0;
            autoRunning = false;
            updateChart();
            document.getElementById('status').innerHTML = '<div class="status-item">Status: Resetado</div>';
        }

        function updateStatus(status) {
            const statusPanel = document.getElementById('status');
            statusPanel.innerHTML = `
                <div class="status-item">Barra: ${status.bar_number || 0}</div>
                <div class="status-item">Perna: ${status.current_leg || 'None'}</div>
                <div class="status-item">Status: ${status.market_status}</div>
                <div class="status-item">Day High: ${status.day_high ? status.day_high.toFixed(0) : 'N/A'}</div>
                <div class="status-item">Day Low: ${status.day_low ? status.day_low.toFixed(0) : 'N/A'}</div>
                <div class="status-item">75%: ${status.leg_75 ? status.leg_75.toFixed(0) : 'N/A'}</div>
            `;
        }

        function updateChart() {
            if (simulatedData.length === 0) return;
            
            const processedBars = reader.all_bars;
            const x = processedBars.map((_, i) => i);
            
            // Candlestick trace
            const candlestick = {
                x: x,
                open: processedBars.map(bar => bar.open),
                high: processedBars.map(bar => bar.high),
                low: processedBars.map(bar => bar.low),
                close: processedBars.map(bar => bar.close),
                type: 'candlestick',
                name: 'Price',
                increasing: {fillcolor: '#00ff00', line: {color: '#00aa00'}},
                decreasing: {fillcolor: '#ff0000', line: {color: '#aa0000'}}
            };

            const traces = [candlestick];

            // Adiciona níveis (SEM estender para a esquerda)
            if (reader.day_high && reader.day_low && currentBarIndex > 0) {
                const startX = 0;
                const endX = currentBarIndex - 1;
                
                traces.push({
                    x: [startX, endX],
                    y: [reader.day_high, reader.day_high],
                    mode: 'lines',
                    name: 'Day High',
                    line: {color: '#ffff00', dash: 'dash', width: 1}
                });

                traces.push({
                    x: [startX, endX],
                    y: [reader.day_low, reader.day_low],
                    mode: 'lines',
                    name: 'Day Low', 
                    line: {color: '#ffff00', dash: 'dash', width: 1}
                });

                if (reader.day_50_percent) {
                    traces.push({
                        x: [startX, endX],
                        y: [reader.day_50_percent, reader.day_50_percent],
                        mode: 'lines',
                        name: 'Day 50%',
                        line: {color: '#00ffff', dash: 'dot', width: 1}
                    });
                }
            }

            // Adiciona nível de 75% (SEMPRE visível se existe)
            if (reader.leg_75_percent && currentBarIndex > 0) {
                traces.push({
                    x: [0, currentBarIndex - 1],
                    y: [reader.leg_75_percent, reader.leg_75_percent],
                    mode: 'lines',
                    name: '75% Retracement',
                    line: {color: '#ff00ff', width: 2}
                });
            }

            // Adiciona sinais
            const signalColors = {
                'leg_start': '#00ff00',      // Verde - perna de tendência
                'lateral_start': '#ffff00', // Amarelo - primeira barra lateral
                'new_leg': '#00aa00',       // Verde escuro - nova perna
                'leg_end': '#ffaa00',       // Laranja - fim de perna
                '75_correction': '#ff0000'  // Vermelho - correção 75%
            };

            reader.signals.forEach(signal => {
                if (signal.x < processedBars.length) {
                    const bar = processedBars[signal.x];
                    traces.push({
                        x: [signal.x],
                        y: [signal.type.includes('leg') ? bar.high + 200 : bar.low - 200],
                        mode: 'markers',
                        name: signal.type,
                        marker: {
                            color: signalColors[signal.type] || '#ffffff',
                            size: 12,
                            symbol: signal.type === '75_correction' ? 'x' : 'circle'
                        },
                        showlegend: false
                    });
                }
            });

            const layout = {
                title: 'Price Action Algorithm - Real Time Analysis',
                plot_bgcolor: '#1e1e1e',
                paper_bgcolor: '#2a2a2a',
                font: {color: '#ffffff'},
                xaxis: {
                    title: 'Barra',
                    gridcolor: '#404040'
                },
                yaxis: {
                    title: 'Preço',
                    gridcolor: '#404040'
                },
                legend: {
                    bgcolor: 'rgba(0,0,0,0.5)'
                }
            };

            Plotly.newPlot('chart', traces, layout);
        }

        // Inicializar
        generateData();
    </script>
</body>
</html>