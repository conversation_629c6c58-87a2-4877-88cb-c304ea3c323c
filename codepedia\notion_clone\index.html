<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Página de Blocos</title>
    <style>
        body { font-family: sans-serif; background-color: #f4f4f4; color: #333; margin: 2rem; }
        .container { max-width: 800px; margin: auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #111; }
        .block-form { display: flex; align-items: center; margin-bottom: 0.5rem; gap: 10px; }
        .block-form input[type="text"] { flex-grow: 1; padding: 10px; border: 1px solid #ccc; border-radius: 4px; font-size: 1rem; }
        .block-form button { padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-update { background-color: #007bff; color: white; }
        .btn-delete { background-color: #dc3545; color: white; }
        .add-form { margin-top: 2rem; border-top: 1px solid #eee; padding-top: 1.5rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Minha Página</h1>

        <div id="blocks-list">
            {% for idx, block_content in blocks %}
            <form action="{{ url_for('update_block', block_id=idx) }}" method="POST" class="block-form">
                <input type="text" name="content" value="{{ block_content }}" required>
                <button type="submit" class="btn-update">Salvar</button>
                <form action="{{ url_for('delete_block', block_id=idx) }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn-delete">X</button>
                </form>
            </form>
            {% endfor %}
        </div>

        <div class="add-form">
            <form action="{{ url_for('add_block') }}" method="POST" class="block-form">
                <input type="text" name="content" placeholder="Digite algo e pressione Enter..." required>
                <button type="submit" class="btn-update">Adicionar Bloco</button>
            </form>
        </div>
    </div>
</body>
</html>