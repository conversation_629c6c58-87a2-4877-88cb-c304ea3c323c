# este script tem como objetivo usar como referencia as abreviações do al brooks presentes em BPA e transformar elas em algortimos e usar para criar um algoritmo de negociação.

_1LD    = "First_Leg_Down" # primeira perna de baixa do dia
_1LU    = "First_Leg_Up" # primeira perna de alta do dia
_2LD    = "Second_Leg_Down" # segunda perna de baixa do dia
_2LU    = "Second_Leg_Up" # segunda perna de alta do dia
_3LD    = "Third_Leg_Down" # terceira perna de baixa do dia

_1MLD   = "strong enough bear breakout for at least One More small Leg Down" # rompimento forte de baixa para pelo menos mais uma perna de baixa
_1MLU	= "strong enough bull breakout for at least One More small Leg Up" # rompimento forte de alta para pelo menos mais uma perna de alta
_1P	    = "First Pause or Pullback in a strong trend" # primeira pause em uma tendencia forte
_20GB	= "Twenty Gap Bars, about 20 consecutive bars that have not touched the moving average" # vinte barras sem tocar a média móvel
_20GBB	= "20 Gap Bar Buy setup. After about 20 or more bars above the exponential moving average, bulls will look to buy a pullback to the exponential moving average" # Após 20 barras acima da média móvel, os bulls vão olhar para comprar uma correção para a média móvel
_20GBS  =	"20 Gap Bar Sell setup. After about 20 or more bars below the exponential moving average bears will look to sell a pullback to the exponential moving average" # Após 20 barras abaixo da média móvel, os bears vão olhar para vender uma correção para a média móvel
_2BR	= "Two Bar Reversal" # reversão em duas barras
_2E	    = "Second Entry" # segunda entrada
_2EB	= "Second buy signal or strong Enough bull to breakout to Buy" # sinal de compra ou rompimento forte de alta para comprar
_2ES    = "Second sell signal or strong Enough bear to breakout to Sell"
