# Importamos as bibliotecas necessárias
import MetaTrader5 as mt5
import pandas as pd # A convenção é importar pandas como 'pd'
from datetime import datetime
import mplfinance as mpf
import tkinter as tk
# (Aqui viriam as outras importações: mt5, pandas, mplfinance...)
# (<PERSON> também as funções que já criamos para buscar e plotar os dados)
import MetaTrader5 as mt5
import matplotlib.pyplot as plt
import time

# ... código de inicialização do MT5 ...

# Ativar o modo interativo do Matplotlib
plt.ion()
fig, ax = plt.subplots()
precos = [] # Lista para guardar os preços dos ticks
linha, = ax.plot(precos) # Cria a linha do gráfico

print("A iniciar a captura de ticks em tempo real... (Pressione Ctrl+C para parar)")

try:
    while True:
        tick = mt5.symbol_info_tick("BTCUSD")
        if tick:
            # Adiciona o novo preço de venda (ask) à nossa lista
            precos.append(tick.ask)
            
            # Atualiza os dados da linha no gráfico
            linha.set_ydata(precos)
            linha.set_xdata(range(len(precos)))
            
            # Reajusta os limites do gráfico e redesenha
            ax.relim()
            ax.autoscale_view()
            fig.canvas.draw()
            fig.canvas.flush_events()
        
        time.sleep(0.5) # Pequena pausa para não sobrecarregar
except KeyboardInterrupt:
    print("Captura interrompida.")
    mt5.shutdown()