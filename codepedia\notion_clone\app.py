import os
import json
from flask import Flask, render_template, request, redirect, url_for

app = Flask(__name__)

# Define o caminho para nosso arquivo de armazenamento
DATA_DIR = 'data'
DATA_FILE = os.path.join(DATA_DIR, 'blocks.json')

def read_blocks():
    """Lê os blocos do arquivo JSON."""
    if not os.path.exists(DATA_FILE):
        return []
    try:
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return []

def write_blocks(blocks):
    """Escreve os blocos no arquivo JSON."""
    # Garante que o diretório 'data' exista
    os.makedirs(DATA_DIR, exist_ok=True)
    with open(DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(blocks, f, indent=4, ensure_ascii=False)

# Rota principal (Read)
@app.route('/')
def index():
    blocks = read_blocks()
    # Usamos enumerate para ter acesso ao índice de cada bloco
    return render_template('index.html', blocks=enumerate(blocks))

# Rota para adicionar um novo bloco (Create)
@app.route('/add', methods=['POST'])
def add_block():
    content = request.form.get('content')
    if content:
        blocks = read_blocks()
        blocks.append(content)
        write_blocks(blocks)
    return redirect(url_for('index'))

# Rota para atualizar um bloco (Update)
@app.route('/update/<int:block_id>', methods=['POST'])
def update_block(block_id):
    blocks = read_blocks()
    new_content = request.form.get('content')
    if 0 <= block_id < len(blocks):
        blocks[block_id] = new_content
        write_blocks(blocks)
    return redirect(url_for('index'))

# Rota para deletar um bloco (Delete)
@app.route('/delete/<int:block_id>', methods=['POST'])
def delete_block(block_id):
    blocks = read_blocks()
    if 0 <= block_id < len(blocks):
        blocks.pop(block_id)
        write_blocks(blocks)
    return redirect(url_for('index'))

if __name__ == '__main__':
    app.run(debug=True)