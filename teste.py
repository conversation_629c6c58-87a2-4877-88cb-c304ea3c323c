import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots

class PriceActionReader:
    def __init__(self):
        # Estado atual do mercado
        self.bars_processed = 0
        self.current_leg = None  # 'down', 'up', 'lateral'
        self.leg_start_price = None
        self.leg_extreme_price = None
        self.leg_75_percent = None
        
        # Níveis do dia (nomenclatura padronizada)
        self.hod = None  # High of Day
        self.lod = None  # Low of Day
        self.mpd = None  # Mid Point of Day
        self.day_range = None
        
        # Níveis do dia anterior (para implementação futura)
        self.hoy = None  # High of Yesterday
        self.loy = None  # Low of Yesterday
        
        # Tracking de barras
        self.previous_bar = None
        self.current_bar = None
        self.all_bars = []
        
        # Estado operacional
        self.market_status = 'neutral'
        self.signals = []
        
    def process_bar(self, bar_data):
        """Processa uma nova barra"""
        self.previous_bar = self.current_bar
        self.current_bar = bar_data
        self.bars_processed += 1
        self.all_bars.append(bar_data)
        
        if self.bars_processed == 1:
            return self._process_first_bar()
        else:
            return self._process_subsequent_bar()
    
    def _process_first_bar(self):
        """Analisa primeira barra e define contexto inicial"""
        bar = self.current_bar
        
        # Define níveis iniciais do dia
        self.hod = bar['high']
        self.lod = bar['low']
        self.day_range = self.hod - self.lod
        self.mpd = self.lod + (self.day_range * 0.5)
        
        # Analisa tipo da primeira barra
        body_size = abs(bar['close'] - bar['open'])
        body_percentage = body_size / self.day_range if self.day_range > 0 else 0
        
        # SEMPRE marca primeira barra como perna (mesmo se lateral)
        self.market_status = 'trend'
        
        if bar['close'] > bar['open']:
            self.current_leg = 'up'
            self.leg_start_price = self.lod
            self.leg_extreme_price = self.hod
        else:
            self.current_leg = 'down'
            self.leg_start_price = self.hod
            self.leg_extreme_price = self.lod
            
        self._calculate_75_percent()
        
        # Adiciona sinal
        signal_type = 'leg_start' if body_percentage > 0.5 else 'lateral_start'
        self.signals.append({
            'bar': self.bars_processed - 1,
            'type': signal_type,
            'direction': self.current_leg,
            'price': bar['close']
        })
        
        return self._get_status()
    
    def _process_subsequent_bar(self):
        """Processa barras após a primeira"""
        bar = self.current_bar
        
        # Atualiza níveis do dia se necessário
        if bar['high'] > self.hod:
            self.hod = bar['high']
            self._update_day_levels()
            
        if bar['low'] < self.lod:
            self.lod = bar['low']
            self._update_day_levels()
        
        # Processa baseado na perna atual
        if self.current_leg == 'down':
            self._process_down_leg()
        elif self.current_leg == 'up':
            self._process_up_leg()
        else:
            self._process_neutral_state()
            
        return self._get_status()
    
    def _process_down_leg(self):
        """Processa perna de baixa ativa"""
        bar = self.current_bar
        prev_bar = self.previous_bar
        
        # Verifica se atualizou mínima
        if bar['low'] < self.leg_extreme_price:
            self.leg_extreme_price = bar['low']
            self._calculate_75_percent()
            return
        
        # Verifica correção de 75%
        if bar['high'] >= self.leg_75_percent:
            self.current_leg = None
            self.market_status = 'lateral'
            self.signals.append({
                'bar': self.bars_processed - 1,
                'type': '75_correction',
                'price': bar['high']
            })
            return
        
        # Verifica fim da perna
        if (bar['low'] > prev_bar['low'] and bar['high'] > prev_bar['high']):
            self.signals.append({
                'bar': self.bars_processed - 1,
                'type': 'leg_end',
                'price': bar['close']
            })
            self.current_leg = None
    
    def _process_up_leg(self):
        """Processa perna de alta ativa"""
        bar = self.current_bar
        prev_bar = self.previous_bar
        
        # Verifica se atualizou máxima
        if bar['high'] > self.leg_extreme_price:
            self.leg_extreme_price = bar['high']
            self._calculate_75_percent()
            return
        
        # Verifica correção de 75%
        if bar['low'] <= self.leg_75_percent:
            self.current_leg = None
            self.market_status = 'lateral'
            self.signals.append({
                'bar': self.bars_processed - 1,
                'type': '75_correction',
                'price': bar['low']
            })
            return
            
        # Verifica fim da perna
        if (bar['high'] < prev_bar['high'] and bar['low'] < prev_bar['low']):
            self.signals.append({
                'bar': self.bars_processed - 1,
                'type': 'leg_end',
                'price': bar['close']
            })
            self.current_leg = None
    
    def _process_neutral_state(self):
        """Processa quando não há perna ativa - detecta nova perna"""
        if self.bars_processed < 2:
            return
            
        bar = self.current_bar
        prev_bar = self.previous_bar
        
        # Verifica se é barra de tendência
        body_size = abs(bar['close'] - bar['open'])
        bar_range = bar['high'] - bar['low']
        
        if bar_range == 0:
            return
            
        body_percentage = body_size / bar_range
        is_trend_bar = body_percentage > 0.5
        
        if not is_trend_bar:
            return
        
        # Calcula median price da barra atual
        median_price = (bar['high'] + bar['low']) / 2
        
        # CONDIÇÃO 1: Barra trend + fechamento além + 50% além da extremidade
        close_beyond = ((bar['close'] >= prev_bar['high']) or 
                       (bar['close'] <= prev_bar['low']))
        
        median_beyond = ((median_price >= prev_bar['high']) or 
                        (median_price <= prev_bar['low']))
        
        if close_beyond and median_beyond:
            # Define direção da nova perna
            if bar['close'] > bar['open']:
                self.current_leg = 'up'
                self.leg_start_price = min(bar['low'], prev_bar['low'])
                self.leg_extreme_price = bar['high']
            else:
                self.current_leg = 'down' 
                self.leg_start_price = max(bar['high'], prev_bar['high'])
                self.leg_extreme_price = bar['low']
            
            self.market_status = 'trend'
            self._calculate_75_percent()
            self.signals.append({
                'bar': self.bars_processed - 1,
                'type': 'new_leg',
                'direction': self.current_leg,
                'price': bar['close']
            })
    
    def _calculate_75_percent(self):
        """Calcula nível de 75% da perna atual"""
        if self.current_leg == 'down':
            leg_amplitude = self.leg_start_price - self.leg_extreme_price
            self.leg_75_percent = self.leg_extreme_price + (leg_amplitude * 0.75)
        elif self.current_leg == 'up':
            leg_amplitude = self.leg_extreme_price - self.leg_start_price
            self.leg_75_percent = self.leg_extreme_price - (leg_amplitude * 0.75)
    
    def _update_day_levels(self):
        """Atualiza níveis do dia quando há nova HOD/LOD"""
        self.day_range = self.hod - self.lod
        self.mpd = self.lod + (self.day_range * 0.5)
    
    def _get_status(self):
        """Retorna status atual completo"""
        return {
            'bar_number': self.bars_processed,
            'current_leg': self.current_leg,
            'market_status': self.market_status,
            'hod': self.hod,
            'lod': self.lod,
            'mpd': self.mpd,
            'leg_extreme': self.leg_extreme_price,
            'leg_75': self.leg_75_percent,
            'day_range': self.day_range
        }

class MT5PriceActionAnalyzer:
    def __init__(self):
        self.reader = PriceActionReader()
        self.connected = False
        
    def connect_mt5(self):
        """Conecta ao MT5"""
        if not mt5.initialize():
            print("Falha ao conectar MT5")
            return False
            
        print("Conectado ao MT5 com sucesso!")
        print(f"Versão: {mt5.version()}")
        self.connected = True
        return True
    
    def get_daily_data(self, symbol="WINV25", date=None):
        """
        Busca dados do dia especificado
        symbol: símbolo (padrão WINV25 - Mini Índice)
        date: data específica (padrão: hoje)
        """
        if not self.connected:
            print("MT5 não conectado!")
            return None
            
        if date is None:
            date = datetime.now().date()    

        start_time = datetime.combine(date, datetime.min.time().replace(hour=9, minute=0))
        end_time = datetime.combine(date, datetime.min.time().replace(hour=12, minute=55))
    
        # Debug - verificar o que está disponível
        print(f"Símbolo: {symbol}")
        print(f"Data solicitada: {date}")
        print(f"Período: {start_time} até {end_time}")

        # Buscar dados
        rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M5, start_time, end_time)

        # Debug - ver o que voltou
        if rates is not None:
            primeiro_dado = pd.to_datetime(rates[0]['time'], unit='s')
            ultimo_dado = pd.to_datetime(rates[-1]['time'], unit='s')
            print(f"Primeiro dado: {primeiro_dado}")
            print(f"Último dado: {ultimo_dado}")
            print(f"Total de barras: {len(rates)}")
        
        # Trocar essas linhas:
        start_time = datetime.combine(date, datetime.min.time().replace(hour=9, minute=0))
        end_time = datetime.combine(date, datetime.min.time().replace(hour=17, minute=55))

        # E adicionar debug:
        print(f"Buscando dados de {start_time} até {end_time}")
        
        # Busca dados de 5 minutos
        rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M5, start_time, end_time)
        
        if rates is None or len(rates) == 0:
            print(f"Nenhum dado encontrado para {symbol} em {date}")
            return None
            
        # Converte para DataFrame
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        
        print(f"Dados obtidos: {len(df)} barras de 5 minutos")
        print(f"Período: {df['time'].iloc[0]} até {df['time'].iloc[-1]}")
        
        return df
    
    def analyze_day(self, df):
        """Analisa um dia completo com o algoritmo"""
        if df is None or len(df) == 0:
            return None
            
        # Reset do reader para novo dia
        self.reader = PriceActionReader()
        
        # Processa cada barra
        for idx, row in df.iterrows():
            bar_data = {
                'open': row['open'],
                'high': row['high'],
                'low': row['low'],
                'close': row['close'],
                'time': row['time'] 
            }
            
            status = self.reader.process_bar(bar_data)
            
            # Debug primeira barra
            if idx == 0:
                print("\n=== PRIMEIRA BARRA ===")
                print(f"OHLC: {row['open']:.0f} | {row['high']:.0f} | {row['low']:.0f} | {row['close']:.0f}")
                print(f"HOD: {status['hod']:.0f}, LOD: {status['lod']:.0f}, MPD: {status['mpd']:.0f}")
                print(f"Perna: {status['current_leg']}, Status: {status['market_status']}")
                if status['leg_75']:
                    print(f"75% Level: {status['leg_75']:.0f}")
        
        return self.reader
    
    def plot_analysis(self, df, reader):
        """Plota análise com Plotly"""
        if df is None or reader is None:
            return
            
        fig = make_subplots(rows=1, cols=1, subplot_titles=[f"Price Action Analysis - {len(df)} barras"])
        
        # Candlestick
        fig.add_trace(go.Candlestick(
            x=df.index,
            open=df['open'],
            high=df['high'],
            low=df['low'],
            close=df['close'],
            name="Price"
        ))
        
        # Níveis do dia
        if reader.hod and reader.lod:
            # HOD
            fig.add_hline(y=reader.hod, line_dash="dash", line_color="yellow", 
                         annotation_text="HOD")
            
            # LOD
            fig.add_hline(y=reader.lod, line_dash="dash", line_color="yellow",
                         annotation_text="LOD")
            
            # MPD
            if reader.mpd:
                fig.add_hline(y=reader.mpd, line_dash="dot", line_color="cyan",
                             annotation_text="MPD")
        
        # 75% Level
        if reader.leg_75_percent:
            fig.add_hline(y=reader.leg_75_percent, line_color="magenta", line_width=2,
                         annotation_text="75%")
        
        # Sinais
        signal_colors = {
            'leg_start': 'green',
            'lateral_start': 'yellow', 
            'new_leg': 'darkgreen',
            'leg_end': 'orange',
            '75_correction': 'red'
        }
        
        for signal in reader.signals:
            if signal['bar'] < len(df):
                bar_data = df.iloc[signal['bar']]
                fig.add_trace(go.Scatter(
                    x=[signal['bar']],
                    y=[signal['price']],
                    mode='markers',
                    marker=dict(
                        size=12,
                        color=signal_colors.get(signal['type'], 'white'),
                        symbol='circle'
                    ),
                    name=signal['type'],
                    showlegend=False
                ))
        
        # Layout
        fig.update_layout(
            title=f"Price Action Algorithm - Análise do Dia",
            xaxis_title="Barra",
            yaxis_title="Preço",
            height=1000,
            template="plotly_dark"
        )
        
        fig.show()
        
        # Status final
        final_status = reader._get_status()
        print(f"\n=== STATUS FINAL ===")
        print(f"Barras processadas: {final_status['bar_number']}")
        print(f"HOD: {final_status['hod']:.0f}, LOD: {final_status['lod']:.0f}")
        print(f"Range do dia: {final_status['day_range']:.0f} pontos")
        print(f"Perna atual: {final_status['current_leg']}")
        print(f"Sinais gerados: {len(reader.signals)}")
    
    def run_analysis(self, symbol="WINV25", date=None):
        """Executa análise completa"""
        if not self.connect_mt5():
            return
            
        print(f"\nBuscando dados para {symbol}...")
        df = self.get_daily_data(symbol, date)
        
        if df is not None:
            print("\nAnalisando com algoritmo Price Action...")
            reader = self.analyze_day(df)
            
            print("\nGerando gráfico...")
            self.plot_analysis(df, reader)
        
        mt5.shutdown()

# Uso
if __name__ == "__main__":
    analyzer = MT5PriceActionAnalyzer()
    
    # Analisa hoje
    analyzer.run_analysis()
    
    # Ou analisa data específica
    # analyzer.run_analysis(date=datetime(2024, 12, 19).date())